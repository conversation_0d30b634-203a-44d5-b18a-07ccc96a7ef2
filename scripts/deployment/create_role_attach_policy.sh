#!/bin/bash
set -x

# Load environment variables
source ./load_env.sh

# Create the role
aws iam create-role --role-name LlmTriageLambdaRole --assume-role-policy-document file://trust-policy.json

# Create the policy
aws iam create-policy --policy-name LlmTriageLambdaPolicy --policy-document file://permissions-policy.json

# Attach the policy to the role
aws iam attach-role-policy --role-name LlmTriageLambdaRole --policy-arn arn:aws:iam::${AWS_ACCOUNT_ID}:policy/LlmTriageLambdaPolicy