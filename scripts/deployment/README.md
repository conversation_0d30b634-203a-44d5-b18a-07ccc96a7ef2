# Deployment Scripts

Automated deployment scripts for AWS infrastructure.

## Environment Setup

Ensure your `.env` file contains:
```
AWS_ACCOUNT_ID=your-account-id
API_ID=your-api-gateway-id  
AWS_ACCESS_KEY=your-access-key
AWS_SECRET_ACCESS_KEY=your-secret-key
```

## Deployment Order

1. `create_dynamo_table.sh` - Create DynamoDB table
2. `create_role_attach_policy.sh` - Create IAM role and policies
3. `create_lambda_function.sh` - Deploy Lambda function
4. `create_api.sh` - Create API Gateway
5. `api_permission_lambda.sh` - Set API permissions
6. `insert_dynamo_table.sh` - Insert clinic rules

## Usage

```bash
# Deploy all components
./deploy.sh

# Or deploy individually
./create_lambda_function.sh
```
