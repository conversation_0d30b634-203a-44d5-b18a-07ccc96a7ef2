#!/bin/bash

# Main deployment script for Titan LLM Triage System
set -e

echo "🚀 Starting Titan LLM Triage System Deployment"
echo "=============================================="

# Check if environment variables are set
source ./load_env.sh

echo ""
echo "📋 Deployment Plan:"
echo "1. Create S3 bucket for logs"
echo "2. Create DynamoDB table"
echo "3. Create IAM role and policies"  
echo "4. Deploy Lambda function"
echo "5. Create API Gateway"
echo "6. Set API permissions"
echo "7. Insert clinic rules"

read -p "Continue with deployment? (y/N) " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "❌ Deployment cancelled"
    exit 0
fi

echo ""
echo "🪣 Step 1: Creating S3 bucket for logs..."
./S3_creation.sh

echo ""
echo "🏗️ Step 2: Creating DynamoDB table..."
./create_dynamo_table.sh

echo ""
echo "🔐 Step 3: Creating IAM role and policies..."  
./create_role_attach_policy.sh

echo ""
echo "⚡ Step 4: Deploying Lambda function..."
./create_lambda_function.sh

echo ""
echo "🌐 Step 5: Creating API Gateway..."
./create_api.sh

echo ""
echo "🔑 Step 6: Setting API permissions..."
./api_permission_lambda.sh

echo ""
echo "📊 Step 7: Inserting clinic rules..."
./insert_dynamo_table.sh

echo ""
echo "✅ Deployment completed successfully!"
echo "🎉 Your Titan LLM Triage System is ready!"
