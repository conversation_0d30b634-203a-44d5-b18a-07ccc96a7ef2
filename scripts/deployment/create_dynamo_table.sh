#!/bin/bash
set -x
# This script creates a DynamoDB table named ClinicUrgencyRules.

echo "Creating DynamoDB table: ClinicUrgencyRules..."

aws dynamodb create-table \
    --table-name ClinicUrgencyRules \
    --attribute-definitions AttributeName=client_id,AttributeType=S \
    --key-schema AttributeName=client_id,KeyType=HASH \
    --billing-mode PAY_PER_REQUEST

echo "✅ Table creation request sent successfully."
echo "Please check the AWS console for the table's status."