#!/bin/bash

# Helper script to load environment variables from .env file
# Usage: source ./load_env.sh

if [ -f "../../.env" ]; then
    export $(cat ../../.env | grep -v '^#' | grep -v '^$' | xargs)
    echo "✅ Environment variables loaded from ../../.env"
    echo "AWS_ACCOUNT_ID: ${AWS_ACCOUNT_ID}"
    echo "API_ID: ${API_ID}"
else
    echo "❌ Error: .env file not found in parent directory"
    exit 1
fi