#!/bin/bash
set -x

# Load environment variables
source ./load_env.sh

aws lambda create-function \
    --function-name LlmTriageFunction \
    --runtime python3.11 \
    --role arn:aws:iam::${AWS_ACCOUNT_ID}:role/LlmTriageLambdaRole \
    --handler lambda_function.lambda_handler \
    --zip-file fileb://lambda_functions/deployment-package.zip \
    --timeout 30 \
    --environment "Variables={TABLE_NAME=ClinicUrgencyRules,LOG_BUCKET=titan-mlops-logs}"