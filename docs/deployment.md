# Deployment Guide

## Overview

This guide covers the deployment of the LLM Triage System on AWS infrastructure using automated scripts and Infrastructure as Code principles.

## Prerequisites

### System Requirements
- **AWS CLI**: Version 2.x or higher
- **Python**: Version 3.9 or higher
- **Bash**: Unix-compatible shell
- **AWS Account**: With appropriate permissions

### AWS Permissions Required
- IAM: CreateRole, AttachRolePolicy, CreatePolicy
- Lambda: CreateFunction, UpdateFunctionCode
- API Gateway: CreateApi, CreateRoute
- DynamoDB: CreateTable, PutItem
- CloudWatch: CreateLogGroup (automatic)

## Environment Setup

### 1. Clone and Navigate
```bash
git clone <repository-url>
cd llm-triage
```

### 2. Configure Environment Variables
```bash
cp .env.example .env
```

Edit `.env` with your AWS configuration:
```bash
# AWS Configuration
AWS_ACCOUNT_ID=************
API_ID=your-api-gateway-id
AWS_ACCESS_KEY=AKIA...
AWS_SECRET_ACCESS_KEY=...

# API Keys
ANTHROPIC_API_KEY=sk-ant-...
OPENAI_API_KEY=sk-...

# Application Settings
LOG_LEVEL=INFO
TABLE_NAME=ClinicUrgencyRules
LOG_BUCKET=llm-triage-logs
```

### 3. Install Dependencies
```bash
pip install -r requirements.txt
```

## Deployment Methods

### Method 1: Automated Deployment (Recommended)

Use the main deployment script for complete setup:

```bash
cd scripts/deployment
./deploy.sh
```

This script will:
1. ✅ Create DynamoDB table
2. ✅ Create IAM roles and policies
3. ✅ Deploy Lambda function
4. ✅ Create API Gateway
5. ✅ Set API permissions
6. ✅ Insert clinic rules

### Method 2: Manual Step-by-Step

If you prefer manual control or need to debug:

```bash
cd scripts/deployment

# Step 1: Create DynamoDB table
./create_dynamo_table.sh

# Step 2: Create IAM role and policies
./create_role_attach_policy.sh

# Step 3: Deploy Lambda function
./create_lambda_function.sh

# Step 4: Create API Gateway
./create_api.sh

# Step 5: Set API permissions (requires API_ID from step 4)
./api_permission_lambda.sh

# Step 6: Insert clinic rules
./insert_dynamo_table.sh
```

## Infrastructure Components

### DynamoDB Table
- **Name**: `ClinicUrgencyRules`
- **Purpose**: Stores clinic-specific triage rules
- **Partition Key**: `client_id`

### Lambda Function
- **Name**: `LlmTriageFunction`
- **Runtime**: Python 3.9
- **Handler**: `lambda_function.lambda_handler`
- **Timeout**: 30 seconds
- **Memory**: 512 MB

### IAM Role
- **Name**: `LlmTriageLambdaRole`
- **Policies**:
  - `LlmTriageLambdaPolicy` (custom)
  - Permissions for DynamoDB, CloudWatch, Bedrock

### API Gateway
- **Type**: HTTP API
- **Name**: `LlmTriageApi`
- **Integration**: Lambda proxy integration
- **Endpoint**: `POST /triage`

## Verification

### 1. Test Lambda Function
```bash
aws lambda invoke \
  --function-name LlmTriageFunction \
  --payload file://infrastructure/aws/api/payload.json \
  response.json

cat response.json
```

### 2. Test API Gateway
```bash
# Get API URL from AWS Console or CLI
API_URL="https://${API_ID}.execute-api.us-west-2.amazonaws.com/default/LlmTriageFunction"

curl -X POST $API_URL \
  -H "Content-Type: application/json" \
  -d '{
    "client_id": "clinic-001",
    "referral_text": "Patient with chest pain"
  }'
```

### 3. Verify DynamoDB
```bash
aws dynamodb scan --table-name ClinicUrgencyRules
```

## Monitoring and Logs

### CloudWatch Logs
- **Log Group**: `/aws/lambda/LlmTriageFunction`
- **Retention**: 14 days (configurable)

### Monitoring Commands
```bash
# View recent logs
aws logs describe-log-streams --log-group-name /aws/lambda/LlmTriageFunction

# Tail logs in real-time
aws logs tail /aws/lambda/LlmTriageFunction --follow
```

### Key Metrics to Monitor
- Lambda invocation count
- Lambda error rate
- Lambda duration
- API Gateway 4xx/5xx errors
- DynamoDB throttling

## Troubleshooting

### Common Issues

#### 1. Permission Denied Errors
```bash
# Check IAM role exists
aws iam get-role --role-name LlmTriageLambdaRole

# Check policy attachment
aws iam list-attached-role-policies --role-name LlmTriageLambdaRole
```

#### 2. Lambda Function Not Found
```bash
# Check function exists
aws lambda get-function --function-name LlmTriageFunction

# Check function configuration
aws lambda get-function-configuration --function-name LlmTriageFunction
```

#### 3. API Gateway Issues
```bash
# List APIs
aws apigatewayv2 get-apis

# Check API configuration
aws apigatewayv2 get-api --api-id $API_ID
```

#### 4. DynamoDB Access Issues
```bash
# Check table status
aws dynamodb describe-table --table-name ClinicUrgencyRules

# Test table access
aws dynamodb get-item \
  --table-name ClinicUrgencyRules \
  --key '{"client_id": {"S": "clinic-001"}}'
```

### Log Analysis

#### Lambda Errors
```bash
# Filter error logs
aws logs filter-log-events \
  --log-group-name /aws/lambda/LlmTriageFunction \
  --filter-pattern "ERROR"
```

#### Performance Issues
```bash
# Check duration metrics
aws logs filter-log-events \
  --log-group-name /aws/lambda/LlmTriageFunction \
  --filter-pattern "REPORT"
```

## Updates and Maintenance

### Update Lambda Function
```bash
# Create new deployment package
cd infrastructure/aws/lambda/llm_triage_function
zip -r deployment-package.zip lambda_function.py

# Update function
aws lambda update-function-code \
  --function-name LlmTriageFunction \
  --zip-file fileb://deployment-package.zip
```

### Update Environment Variables
```bash
aws lambda update-function-configuration \
  --function-name LlmTriageFunction \
  --environment Variables='{
    "TABLE_NAME": "ClinicUrgencyRules",
    "LOG_LEVEL": "INFO"
  }'
```

### Update DynamoDB Data
```bash
# Insert new clinic rules
aws dynamodb put-item \
  --table-name ClinicUrgencyRules \
  --item file://data/config/clinic_rules/new-clinic-rules.json
```

## Security Considerations

1. **Environment Variables**: Never commit `.env` files
2. **IAM Policies**: Follow principle of least privilege
3. **API Security**: Implement proper authentication
4. **Logging**: Avoid logging sensitive medical data
5. **Encryption**: Enable encryption at rest for DynamoDB

## Rollback Procedures

### Rollback Lambda Function
```bash
# List versions
aws lambda list-versions-by-function --function-name LlmTriageFunction

# Rollback to previous version
aws lambda update-alias \
  --function-name LlmTriageFunction \
  --name LIVE \
  --function-version $PREVIOUS_VERSION
```

### Restore DynamoDB Table
```bash
# Create backup before deployment
aws dynamodb create-backup \
  --table-name ClinicUrgencyRules \
  --backup-name pre-deployment-backup

# Restore from backup if needed
aws dynamodb restore-table-from-backup \
  --target-table-name ClinicUrgencyRules-restored \
  --backup-arn $BACKUP_ARN
```

## Performance Optimization

### Lambda Optimization
- **Memory**: Adjust based on usage patterns
- **Timeout**: Set appropriate timeout values
- **Concurrency**: Configure reserved concurrency if needed

### API Gateway Optimization
- **Caching**: Enable caching for repeated requests
- **Throttling**: Set appropriate throttling limits
- **CORS**: Configure CORS if needed for web clients

### Cost Optimization
- **Lambda**: Monitor execution time and memory usage
- **API Gateway**: Consider usage plans for cost control
- **DynamoDB**: Use on-demand billing for variable workloads

## Next Steps

After successful deployment:

1. **Testing**: Run comprehensive evaluation tests
2. **Monitoring**: Set up CloudWatch alarms
3. **Documentation**: Update API documentation
4. **Integration**: Connect to existing medical systems
5. **Scaling**: Plan for production load requirements