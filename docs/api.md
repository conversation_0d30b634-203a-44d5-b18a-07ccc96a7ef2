# API Documentation

## Overview

The LLM Triage API is a RESTful service that classifies medical referrals into appropriate specialties using Large Language Models integrated with AWS services.

## Base URL

```
https://{API_ID}.execute-api.us-west-2.amazonaws.com/default/LlmTriageFunction
```

## Authentication

The API uses AWS API Gateway with AWS IAM for authentication. Ensure your requests include proper AWS Signature Version 4 authentication headers.

## Endpoints

### POST /

Classifies a medical referral text into the appropriate specialty.

#### Request

**Headers:**
- `Content-Type: application/json`
- `Authorization: AWS4-HMAC-SHA256 ...` (AWS Signature)

**Body:**
```json
{
  "client_id": "clinic-001",
  "referral_text": "Patient presents with chest pain and shortness of breath. ECG shows ST elevation. Requesting urgent cardiology consultation."
}
```

#### Response

**Success (200 OK):**
```json
{
  "specialty": "CARDIOLOGY",
  "urgency_status": 1,
  "supporting_evidence": "Patient presents with chest pain and shortness of breath. ECG shows ST elevation.",
  "confidence_score": 0.95
}
```

**Error (400 Bad Request):**
```json
{
  "error": "Invalid request",
  "message": "client_id and referral_text are required"
}
```

**Error (500 Internal Server Error):**
```json
{
  "error": "Processing failed",
  "message": "LLM service temporarily unavailable"
}
```

## Supported Specialties

The API classifies referrals into these specialties:

- **CARDIOLOGY** - Heart and cardiovascular conditions
- **NEUROLOGY** - Brain and nervous system disorders  
- **GYNECOLOGY** - Women's reproductive health
- **HEMATOLOGY_AND_ONCOLOGY** - Blood disorders and cancer
- **ORTHOPEDICS** - Musculoskeletal conditions
- **NUTRITION** - Dietary and metabolic issues

## Request Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| client_id | string | Yes | Clinic identifier for rule-based processing |
| referral_text | string | Yes | The medical referral text to classify |

## Response Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| specialty | string | Classified medical specialty |
| urgency_status | integer | Urgency level (1=urgent, 0=non-urgent) |
| supporting_evidence | string | Key text supporting the classification |
| confidence_score | float | Classification confidence (0.0-1.0) |

## Request Limits

- **Rate Limit**: 100 requests per minute per API key
- **Payload Size**: Maximum 10KB per request
- **Timeout**: 30 seconds per request

## Error Codes

| Code | Description |
|------|-------------|
| 400 | Bad Request - Invalid input format |
| 401 | Unauthorized - Invalid authentication |
| 403 | Forbidden - Insufficient permissions |
| 429 | Too Many Requests - Rate limit exceeded |
| 500 | Internal Server Error - Service unavailable |
| 503 | Service Unavailable - Temporary downtime |

## Usage Examples

### Python

```python
import requests
import json
from botocore.auth import SigV4Auth
from botocore.awsrequest import AWSRequest

def call_triage_api(referral_text, client_id, api_url, access_key, secret_key):
    payload = {
        "client_id": client_id,
        "referral_text": referral_text
    }
    
    request = AWSRequest(
        method='POST',
        url=api_url,
        data=json.dumps(payload),
        headers={'Content-Type': 'application/json'}
    )
    
    SigV4Auth(credentials, 'execute-api', 'us-west-2').add_auth(request)
    
    response = requests.post(
        request.url,
        data=request.body,
        headers=dict(request.headers)
    )
    
    return response.json()
```

### cURL

```bash
curl -X POST https://l4sstl0qe6.execute-api.us-west-2.amazonaws.com/default/LlmTriageFunction \
-H "Content-Type: application/json" \
-d '{
    "client_id": "clinic-001",
    "referral_text": "Patient is a 3-week-old newborn presenting with a distinct heart murmur and difficulty feeding. Parents are concerned about FTT. Recommend immediate evaluation by a specialist."
}'
```
## Monitoring

The API provides CloudWatch metrics for:
- Request count
- Error rate  
- Response latency
- Throttling events
