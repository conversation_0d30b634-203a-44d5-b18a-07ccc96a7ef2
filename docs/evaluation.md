# Evaluation Methodology

## Overview

This document outlines the comprehensive evaluation methodology for the LLM Triage System, including synthetic data generation, performance metrics, and testing procedures.

## Evaluation Framework

### Objective
Assess the accuracy and reliability of the LLM-based medical referral triage system across multiple specialties and urgency levels.

### Scope
- **Specialties**: 6 medical specialties
- **Urgency Levels**: Urgent, non-urgent, and ambiguous cases
- **Sample Size**: 90 synthetic referrals (15 per specialty)
- **Metrics**: Classification accuracy, precision, recall, F1-score

## Synthetic Data Generation

### Data Structure
Each synthetic referral contains:
- **Referral Text**: Realistic medical case description
- **Expected Specialty**: Ground truth classification
- **Urgency Level**: Urgent, non-urgent, or ambiguous
- **Clinical Context**: Relevant medical details

### Generation Process

#### 1. Specialty Distribution
```
CARDIOLOGY: 15 samples (5 urgent, 5 non-urgent, 5 ambiguous)
NEUROLOGY: 15 samples (5 urgent, 5 non-urgent, 5 ambiguous)  
GYNECOLOGY: 15 samples (5 urgent, 5 non-urgent, 5 ambiguous)
HEMATOLOGY_AND_ONCOLOGY: 15 samples (5 urgent, 5 non-urgent, 5 ambiguous)
ORTHOPEDICS: 15 samples (5 urgent, 5 non-urgent, 5 ambiguous)
NUTRITION: 15 samples (5 urgent, 5 non-urgent, 5 ambiguous)
```

#### 2. Clinical Contexts by Specialty

**CARDIOLOGY**
- Chest pain, ECG abnormalities, heart murmurs
- Hypertension, arrhythmias, cardiac catheterization
- Heart failure symptoms, palpitations

**NEUROLOGY**
- Headaches, seizures, memory issues
- Stroke symptoms, neurological deficits
- Movement disorders, cognitive changes

**GYNECOLOGY**
- Pelvic pain, menstrual irregularities
- Pregnancy complications, cervical abnormalities
- Reproductive health concerns

**HEMATOLOGY_AND_ONCOLOGY**
- Blood count abnormalities, bleeding disorders
- Cancer screening, tumor evaluations
- Chemotherapy side effects, lymph node enlargement

**ORTHOPEDICS**
- Joint pain, fractures, mobility issues
- Sports injuries, arthritis symptoms
- Spine conditions, muscle weakness

**NUTRITION**
- Weight management, eating disorders
- Diabetes management, metabolic conditions
- Dietary counseling needs

#### 3. Urgency Classification

**Urgent Cases**
- Immediate medical attention required
- Potential for rapid deterioration
- Time-sensitive conditions

**Non-Urgent Cases**
- Routine consultations
- Chronic condition management
- Preventive care

**Ambiguous Cases**
- Borderline urgency requiring clinical judgment
- Multiple possible interpretations
- Edge cases for algorithm testing

### Data Generation Script

```bash
python -m src.data_generation.synthetic_referrals
```

**Output Files:**
- `data/processed/referral_samples/all_referrals.json`
- Individual specialty files (e.g., `cardiology_referrals.json`)

## Evaluation Metrics

### Primary Metrics

#### 1. Classification Accuracy
```
Accuracy = (Correct Classifications) / (Total Classifications)
```

#### 2. Specialty-Specific Precision
```
Precision = True Positives / (True Positives + False Positives)
```

#### 3. Specialty-Specific Recall
```
Recall = True Positives / (True Positives + False Negatives)
```

#### 4. F1-Score
```
F1 = 2 × (Precision × Recall) / (Precision + Recall)
```

### Secondary Metrics

- **Confusion Matrix**: Cross-specialty classification patterns
- **Response Time**: API response latency
- **Error Rate**: Failed requests and system errors
- **Retry Statistics**: Success rate after retries

### Performance Thresholds

| Metric | Minimum Acceptable | Target |
|--------|-------------------|---------|
| Overall Accuracy | 80% | 90% |
| Specialty Precision | 75% | 85% |
| Specialty Recall | 75% | 85% |
| F1-Score | 75% | 85% |
| Response Time | <5 seconds | <2 seconds |

## Evaluation Process

### 1. Test Execution

#### Running Evaluation
```bash
python -m src.evaluation.llm_triage_evaluator
```

#### Configuration Parameters
- **Concurrency**: 1 (sequential processing)
- **Timeout**: 30 seconds per request
- **Retry Logic**: 3 attempts with exponential backoff
- **Client ID**: "clinic-001" for rule-based processing

### 2. Retry Mechanism

#### Retry Strategy
```python
async def call_api_with_retry(self, session, referral_text, max_retries=3):
    for attempt in range(max_retries + 1):
        try:
            # API call logic
            return response
        except (TimeoutError, HTTP429, HTTP5xx) as e:
            if attempt < max_retries:
                # Exponential backoff: 1s, 2s, 4s + jitter
                delay = (2 ** attempt) + random.uniform(0, 1)
                await asyncio.sleep(delay)
            else:
                raise
```

#### Retry-Eligible Errors
- HTTP 429 (Too Many Requests)
- HTTP 500-504 (Server Errors)
- Timeout exceptions
- Connection errors

### 3. Result Analysis

#### Output Files
- **Detailed Results**: `outputs/evaluation_results/detailed_results_{timestamp}.json`
- **CSV Export**: `outputs/evaluation_results/detailed_results_{timestamp}.csv`
- **Summary Report**: `outputs/evaluation_results/evaluation_report_{timestamp}.md`

#### Result Structure
```json
{
  "referral_id": 1,
  "expected_specialty": "CARDIOLOGY",
  "predicted_specialty": "CARDIOLOGY",
  "specialty_correct": true,
  "response_time": 1.23,
  "retry_count": 0,
  "total_attempts": 1,
  "error_message": null
}
```

## Statistical Analysis

### Confusion Matrix Analysis
```
     CARDIOLO NEUROLOG GYNECOLO HEMATOLO ORTHOPED NUTRITIO
CARDIOLO       15        0        0        0        0        0
NEUROLOG        0       15        0        0        0        0
GYNECOLO        0        0       15        0        0        0
HEMATOLO        0        0        0       15        0        0
ORTHOPED        1        0        0        0       14        0
NUTRITIO        0        0        0        0        0       15
```

### Specialty Performance Analysis
Individual analysis for each specialty including:
- Classification accuracy
- Common misclassification patterns
- Error analysis by urgency level
- Response time distribution

### Error Analysis

#### Common Error Patterns
1. **Cross-Specialty Confusion**: Similar symptoms across specialties
2. **Urgency Misclassification**: Ambiguous cases marked incorrectly
3. **API Failures**: Timeout or service unavailability
4. **Edge Cases**: Unusual medical presentations

#### Root Cause Analysis
- Review failed cases for pattern identification
- Analyze API logs for technical issues
- Correlate errors with specific medical terminology
- Identify improvement opportunities

## Quality Assurance

### Data Validation
- Medical terminology accuracy
- Clinical scenario realism
- Specialty distribution balance
- Urgency level appropriateness

### Model Validation
- Cross-validation with medical experts
- Comparison with existing triage systems
- Sensitivity analysis for edge cases
- Performance stability over time

### Ethical Considerations
- No real patient data used
- HIPAA compliance maintained
- Bias detection and mitigation
- Fairness across demographic groups

## Continuous Improvement

### Performance Monitoring
- Regular evaluation runs (weekly/monthly)
- Performance trend analysis
- Alert thresholds for degradation
- Automated reporting dashboards

### Model Updates
- Retraining based on new data
- Fine-tuning for specialty accuracy
- Prompt engineering optimization
- Rule-based logic refinement

### Evaluation Enhancements
- Expanded test scenarios
- Additional medical specialties
- Real-world validation studies
- Integration testing

## Reporting and Documentation

### Automated Reports
- Performance summaries
- Trend analysis charts
- Error pattern identification
- Recommendations for improvement

### Stakeholder Communication
- Executive summaries
- Technical deep-dives
- Clinical validation reports
- Compliance documentation

## Future Enhancements

### Planned Improvements
1. **Expanded Dataset**: More specialties and edge cases
2. **Real-World Validation**: Clinical pilot programs
3. **Advanced Metrics**: Clinical outcome correlations
4. **Automated A/B Testing**: Continuous model comparison
5. **Integration Testing**: End-to-end workflow validation

### Research Opportunities
- Multi-modal input analysis (images, lab results)
- Temporal pattern recognition
- Predictive triage modeling
- Personalized triage recommendations