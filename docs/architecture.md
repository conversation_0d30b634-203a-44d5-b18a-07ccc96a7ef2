# Architecture Overview

## System Architecture

The LLM Triage System is a cloud-native medical referral classification service built on AWS infrastructure with MLOps principles and serverless architecture.

## High-Level Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Client Apps   │───▶│   API Gateway    │───▶│ Lambda Function │
│                 │    │  (HTTP/REST)     │    │  (LLM Triage)   │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                                          │
                                                          ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   CloudWatch    │◀───│   DynamoDB       │◀───│  Amazon Bedrock │
│   (Monitoring)  │    │ (Clinic Rules)   │    │    (LLM API)    │
└─────────────────┘    └──────────────────┘    └─────────────────┘
          │                                              │
          ▼                                              ▼
┌─────────────────┐                          ┌─────────────────┐
│   S3 Bucket     │                          │   MLOps Logs    │
│ (titan-mlops-   │◀─────────────────────────│   & Metrics     │
│  logs)          │                          │                 │
└─────────────────┘                          └─────────────────┘
```

## Core Components

### 1. API Gateway
- **Type**: HTTP API (API Gateway v2)
- **Protocol**: HTTPS REST
- **Authentication**: AWS IAM (optional - currently public)
- **Endpoints**: POST /default/LlmTriageFunction
- **Features**:
  - Lambda proxy integration
  - Request/response transformation
  - Throttling and rate limiting
  - CORS support

### 2. Lambda Function
- **Runtime**: Python 3.9
- **Handler**: `lambda_function.lambda_handler`
- **Memory**: 512 MB
- **Timeout**: 30 seconds
- **Execution Role**: `LlmTriageLambdaRole`

#### Function Responsibilities
- Input validation and sanitization
- Clinic rule retrieval from DynamoDB
- LLM API calls to Amazon Bedrock
- Response formatting and error handling
- Logging and monitoring

### 3. Amazon Bedrock (LLM Service)
- **Model**: Claude v3 Sonnet
- **Purpose**: Medical text classification
- **Integration**: AWS SDK (boto3)
- **Features**:
  - Medical domain expertise
  - Contextual understanding
  - Structured output generation

### 4. DynamoDB
- **Table**: `ClinicUrgencyRules`
- **Partition Key**: `client_id` (String)
- **Purpose**: Store clinic-specific triage rules
- **Billing Mode**: On-demand
- **Data Structure**:
```json
{
  "client_id": "clinic-001",
  "urgency_rules": {
    "urgent_keywords": ["emergency", "urgent", "stat"],
    "specialty_mappings": {
      "chest pain": "CARDIOLOGY",
      "seizure": "NEUROLOGY"
    }
  }
}
```

### Response Format
```json
{
  "specialty": "CARDIOLOGY",
  "urgency_status": 1,
  "supporting_evidence": "Key text supporting classification",
  "confidence_score": 0.95
}
```

### 5. CloudWatch
- **Log Groups**: `/aws/lambda/LlmTriageFunction`
- **Metrics**: Custom metrics for triage performance
- **Alarms**: Error rate and latency monitoring
- **Retention**: 14 days (configurable)

### 6. S3 Bucket
- **Bucket Name**: `titan-mlops-logs`
- **Purpose**: Store MLOps logs, metrics, and system artifacts
- **Features**:
  - Centralized logging storage
  - MLOps pipeline artifacts
  - Evaluation results archival
  - System performance metrics

## Data Flow

### Request Flow
```
1. Client Request → API Gateway
2. API Gateway → Lambda Function (with validation)
3. Lambda → DynamoDB (fetch clinic rules)
4. Lambda → Amazon Bedrock (LLM classification)
5. Lambda → API Gateway (structured response)
6. API Gateway → Client (final response)
```

### Error Flow
```
1. Error Occurs → Lambda Exception Handler
2. Lambda → CloudWatch (error logging)
3. Lambda → API Gateway (error response)
4. API Gateway → Client (formatted error)
```

## Security Architecture

### Authentication & Authorization
- **API Level**: AWS IAM policies
- **Function Level**: Execution role with minimal permissions
- **Data Level**: DynamoDB resource-based policies

### Network Security
- **Encryption**: TLS 1.2+ for all API communications
- **VPC**: Lambda can optionally run in VPC
- **Secrets**: Environment variables for API keys
- **Data Protection**: Encryption at rest for DynamoDB

### IAM Policies

#### Lambda Execution Role
```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "logs:CreateLogGroup",
        "logs:CreateLogStream", 
        "logs:PutLogEvents"
      ],
      "Resource": "arn:aws:logs:*:*:*"
    },
    {
      "Effect": "Allow",
      "Action": [
        "dynamodb:GetItem",
        "dynamodb:Query"
      ],
      "Resource": "arn:aws:dynamodb:*:*:table/ClinicUrgencyRules"
    },
    {
      "Effect": "Allow",
      "Action": [
        "bedrock:InvokeModel"
      ],
      "Resource": "arn:aws:bedrock:*:*:foundation-model/*"
    }
  ]
}
```

## Scalability & Performance

### Auto-Scaling
- **Lambda**: Automatic scaling up to 1000 concurrent executions
- **API Gateway**: Handles 10,000 requests per second
- **DynamoDB**: On-demand scaling based on traffic
- **Bedrock**: Managed service with built-in scaling

### Performance Optimization
- **Cold Start**: Optimized Lambda package size
- **Connection Pooling**: Reuse DynamoDB connections
- **Caching**: API Gateway response caching (optional)
- **Timeout**: Appropriate timeout values for each service

### Performance Targets
| Metric | Target | Monitoring |
|--------|--------|------------|
| API Latency | < 2 seconds | CloudWatch |
| Lambda Duration | < 10 seconds | X-Ray tracing |
| Error Rate | < 1% | CloudWatch Alarms |
| Availability | > 99.9% | Health checks |

## Monitoring & Observability

### Logging Strategy
```
Application Logs → CloudWatch Logs → Log Insights → Dashboards
```

### Metrics Collection
- **API Gateway**: Request count, latency, error rate
- **Lambda**: Invocation count, duration, error count
- **DynamoDB**: Read/write capacity, throttling
- **Bedrock**: Model invocation metrics

### Distributed Tracing
- **AWS X-Ray**: End-to-end request tracing
- **Correlation IDs**: Request tracking across services
- **Performance Profiling**: Bottleneck identification

### Alerting
```yaml
Alerts:
  - High Error Rate (>5% in 5 minutes)
  - High Latency (>5 seconds 95th percentile)
  - Lambda Throttling
  - DynamoDB Throttling
  - Bedrock Service Errors
```

## Deployment Architecture

### Infrastructure as Code
- **Scripts**: Bash deployment scripts
- **Configuration**: Environment-based settings
- **Versioning**: Lambda function versioning
- **Rollback**: Automated rollback capabilities

### CI/CD Pipeline
```
Code → Build → Test → Deploy → Monitor
  │      │      │       │        │
  │      │      │       │        └─ CloudWatch
  │      │      │       └─ AWS CLI Scripts  
  │      │      └─ Unit/Integration Tests
  │      └─ Package Lambda Function
  └─ Git Repository
```

### Environment Management
- **Development**: Isolated AWS resources
- **Staging**: Production-like environment
- **Production**: Live system with monitoring

## Data Architecture

### Data Sources
- **Input**: Medical referral text + clinic ID
- **Configuration**: Clinic-specific rules in DynamoDB
- **Output**: Specialty classification + confidence

### Data Processing Pipeline
```
Raw Text → Preprocessing → LLM Analysis → Rule Application → Classification
```

### Data Storage
- **Transient**: Request/response data (not persisted)
- **Configuration**: Clinic rules (DynamoDB)
- **Logs**: Application logs (CloudWatch)
- **Metrics**: Performance data (CloudWatch Metrics)

## Integration Patterns

### API Integration
- **Synchronous**: REST API for real-time classification
- **Asynchronous**: Future support for batch processing
- **Webhooks**: Future support for result callbacks

### External Systems
- **EMR Integration**: Electronic Medical Records
- **Clinical Workflows**: Integration with existing triage systems
- **Audit Systems**: Compliance and audit trail integration

## Disaster Recovery

### Backup Strategy
- **DynamoDB**: Point-in-time recovery enabled
- **Lambda Code**: Versioned in S3
- **Configuration**: Infrastructure as Code in Git

### Recovery Procedures
- **RTO**: Recovery Time Objective < 4 hours
- **RPO**: Recovery Point Objective < 1 hour
- **Multi-Region**: Future enhancement for disaster recovery

## Cost Optimization

### Pricing Model
- **API Gateway**: Pay per request
- **Lambda**: Pay per invocation and duration
- **DynamoDB**: On-demand pricing
- **Bedrock**: Pay per token usage

### Cost Controls
- **Monitoring**: AWS Cost Explorer integration
- **Budgets**: Automated alerts for cost thresholds
- **Optimization**: Regular review of resource utilization

## Future Enhancements

### Planned Architecture Improvements

#### 1. Multi-Region Deployment
```
Primary Region (us-west-2) ←→ Secondary Region (us-east-1)
       │                             │
       └─ Cross-region replication ───┘
```

#### 2. Event-Driven Architecture
```
API Gateway → SQS → Lambda → EventBridge → Analytics
```

#### 3. Enhanced Security
- **WAF**: Web Application Firewall
- **KMS**: Customer-managed encryption keys
- **VPC**: Private subnet deployment

#### 4. Advanced Monitoring
- **Custom Dashboards**: Real-time operational views
- **Anomaly Detection**: ML-powered alerting
- **Business Metrics**: Clinical outcome tracking

#### 5. Performance Enhancements
- **Edge Computing**: CloudFront edge locations
- **Caching Layer**: ElastiCache for frequent requests
- **Database Optimization**: DynamoDB Global Tables

## Technology Stack Summary

| Layer | Technology | Purpose |
|-------|------------|---------|
| **API** | AWS API Gateway | Request routing, authentication |
| **Compute** | AWS Lambda | Business logic execution |
| **AI/ML** | Amazon Bedrock | Medical text classification |
| **Database** | Amazon DynamoDB | Configuration storage |
| **Monitoring** | Amazon CloudWatch | Logging and metrics |
| **Security** | AWS IAM | Access control |
| **Deployment** | Bash Scripts | Infrastructure automation |

This architecture provides a scalable, secure, and maintainable foundation for medical referral triage using modern cloud-native patterns and MLOps best practices.