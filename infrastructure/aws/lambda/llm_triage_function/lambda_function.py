import json
import boto3
import os
from datetime import datetime

# Initialize clients outside the handler for reuse
s3_client = boto3.client('s3')
dynamodb = boto3.resource('dynamodb')
bedrock_runtime = boto3.client('bedrock-runtime')

# Get environment variables
TABLE_NAME = os.environ.get('TABLE_NAME', 'ClinicUrgencyRules')
LOG_BUCKET = os.environ.get('LOG_BUCKET', 'titan-mlops-logs')
table = dynamodb.Table(TABLE_NAME)

def construct_prompt(rules, referral_text):
    """Constructs a more direct prompt to improve dynamic field generation."""
    prompt = f"""
        You are a medical triage assistant. Your task is to analyze the following referral and return a structured JSON object.

        **Referral Text:**
        <referral_text>
        {referral_text}
        </referral_text>

        **Rules for Urgency by Specialty:**
        <rules>
        {json.dumps(rules, indent=2)}
        </rules>

        **Your Tasks:**
        1.  **"specialty"**: First, determine the single most likely medical specialty.
        2.  **"urgency_status"**: Second, based on the specialty you chose, determine if the referral is urgent (1) or not urgent (0) by consulting the rules.
        3.  **"supporting_evidence"**: Third, extract a brief snippet from the referral text that justifies your urgency decision.
        4.  **"confidence_score"**: Fourth, provide a confidence score for your urgency decision (from 0.0 to 1.0). A high score (0.9+) means the text directly matches an urgent rule; a medium score (0.6-0.8) means it strongly implies a rule; a low score (<0.6) means it's ambiguous.

        Return ONLY the JSON object.
        """
    return prompt

def lambda_handler(event, context):
    try:
        # 1. Parse input from API Gateway
        body = json.loads(event.get('body', '{}'))
        client_id = body.get('client_id')
        referral_text = body.get('referral_text') # Expecting a single string

        if not client_id or not referral_text:
            return {'statusCode': 400, 'body': json.dumps({'error': 'client_id and referral_text are required'})}

        # 2. Fetch rules from DynamoDB
        response = table.get_item(Key={'client_id': client_id})
        if 'Item' not in response:
            return {'statusCode': 404, 'body': json.dumps({'error': 'Client configuration not found'})}
        rules = response['Item']['rules']

        # 3. Construct prompt and call Bedrock
        prompt = construct_prompt(rules, referral_text)
        
        bedrock_request_body = {
            "anthropic_version": "bedrock-2023-05-31",
            "max_tokens": 1024,
            "messages": [{"role": "user", "content": prompt}],
            "temperature": 0.2,
        }

        bedrock_response = bedrock_runtime.invoke_model(
            modelId='anthropic.claude-3-sonnet-20240229-v1:0', 
            body=json.dumps(bedrock_request_body)
        )
        
        response_body = json.loads(bedrock_response['body'].read())
        llm_output_text = response_body['content'][0]['text']
        
        # 4. Parse the LLM's JSON response
        parsed_output = json.loads(llm_output_text)

        # 5. Log input/output to S3 for MLOps
        log_data = {
            'requestId': context.aws_request_id,
            'timestamp': datetime.utcnow().isoformat(),
            'client_id': client_id,
            'input_text': referral_text,
            'prompt_used': prompt,
            'llm_raw_output': llm_output_text,
            'parsed_output': parsed_output
        }
        log_key = f"logs/{datetime.utcnow().strftime('%Y/%m/%d')}/{context.aws_request_id}.json"
        s3_client.put_object(
            Bucket=LOG_BUCKET,
            Key=log_key,
            Body=json.dumps(log_data)
        )

        # 6. Return structured JSON response
        return {
            'statusCode': 200,
            'headers': {'Content-Type': 'application/json'},
            'body': json.dumps(parsed_output)
        }

    except Exception as e:
        print(f"Error: {e}")
        return {'statusCode': 500, 'body': json.dumps({'error': 'An internal server error occurred'})}