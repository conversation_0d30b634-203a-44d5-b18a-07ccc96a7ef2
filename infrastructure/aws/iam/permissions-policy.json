{"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Action": "logs:CreateLogGroup", "Resource": "arn:aws:logs:us-west-2:797837377135:*"}, {"Effect": "Allow", "Action": ["logs:CreateLogStream", "logs:PutLogEvents"], "Resource": ["arn:aws:logs:us-west-2:797837377135:log-group:/aws/lambda/LlmTriageFunction:*"]}, {"Effect": "Allow", "Action": "dynamodb:GetItem", "Resource": "arn:aws:dynamodb:us-west-2:797837377135:table/ClinicUrgencyRules"}, {"Effect": "Allow", "Action": "bedrock:InvokeModel", "Resource": "arn:aws:bedrock:us-west-2::foundation-model/*"}, {"Effect": "Allow", "Action": "s3:PutObject", "Resource": "arn:aws:s3:::titan-mlops-logs/*"}]}