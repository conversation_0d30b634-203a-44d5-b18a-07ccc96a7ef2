# Infrastructure

Infrastructure as Code for the Titan LLM Triage System.

## AWS Resources

- **lambda/**: AWS Lambda function definitions
- **iam/**: IAM roles and policies  
- **api/**: API Gateway configurations

## Deployment

Use the deployment scripts in `scripts/deployment/` to provision resources:

```bash
cd scripts/deployment
./deploy.sh
```

## Future Enhancements

- **terraform/**: Terraform configurations for infrastructure management
