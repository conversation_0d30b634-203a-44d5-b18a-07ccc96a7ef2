import os
import json
import asyncio
from typing import Dict, List, Tuple
from datetime import datetime
import anthropic
from dotenv import load_dotenv
from src.utils.config_loader import get_config

load_dotenv()

class SyntheticReferralGenerator:
    def __init__(self):
        self.config = get_config()
        self.client = anthropic.Anthropic(api_key=os.getenv('ANTHROPIC_API_KEY'))
        
        # Load configuration
        self.specialties = self.config.get_specialties_list()
        gen_params = self.config.get_generation_params()
        self.urgency_types = gen_params['urgency_types']
        self.ambiguity_levels = gen_params['ambiguity_levels']
        
        # LLM configuration
        self.llm_config = self.config.get_llm_config('data_generation')
        
    def create_prompt(self, specialty: str, urgency: str, ambiguity: str) -> str:
        """Create a detailed prompt for generating synthetic referrals."""
        
        base_prompt = f"""You are a medical professional creating a realistic pediatric referral text for {specialty}. 

Generate a single referral text that is {urgency} and has {ambiguity} ambiguity level.

Guidelines:
- {urgency.upper()}: {'Contains clear urgent indicators requiring immediate specialist attention' if urgency == 'urgent' else 'Routine referral that can be scheduled normally' if urgency == 'non_urgent' else 'Contains mixed signals about urgency level'}
- {ambiguity.upper()} AMBIGUITY: {'Clear keywords and straightforward symptoms' if ambiguity == 'low' else 'Symptoms described without exact medical keywords' if ambiguity == 'medium' else 'Conflicting information or symptoms pointing to multiple specialties'}

Specialty-specific context:
"""
        
        specialty_contexts = self.config.get_specialty_contexts()
        
        base_prompt += specialty_contexts[specialty]
        
        base_prompt += f"""

Generate ONLY the referral text (2-4 sentences) without any labels, explanations, or metadata. The text should sound like it was written by a healthcare provider referring a patient to a specialist.

Example format: "Patient is a [age] presenting with [symptoms]. [Additional clinical details]. [Reason for referral]."
"""
        
        return base_prompt
    
    async def generate_referral(self, specialty: str, urgency: str, ambiguity: str) -> Dict:
        """Generate a single synthetic referral."""
        
        prompt = self.create_prompt(specialty, urgency, ambiguity)
        
        try:
            response = self.client.messages.create(
                model=self.llm_config['model_name'],
                max_tokens=self.llm_config['max_tokens'],
                temperature=self.llm_config['temperature'],
                messages=[{"role": "user", "content": prompt}]
            )
            
            referral_text = response.content[0].text.strip()
            
            # Determine expected urgency status based on type
            urgency_status = 1 if urgency == "urgent" else 0 if urgency == "non_urgent" else None
            
            return {
                "referral_text": referral_text,
                "expected_specialty": specialty,
                "expected_urgency_status": urgency_status,
                "generation_params": {
                    "urgency_type": urgency,
                    "ambiguity_level": ambiguity,
                    "specialty": specialty
                },
                "metadata": {
                    "generated_at": datetime.now().isoformat(),
                    "model": self.llm_config['model_name']
                }
            }
            
        except Exception as e:
            print(f"Error generating referral for {specialty}-{urgency}-{ambiguity}: {e}")
            return None
    
    async def generate_batch(self, specialty: str, count_per_type: int = 5) -> List[Dict]:
        """Generate a batch of referrals for a single specialty."""
        
        tasks = []
        for urgency in self.urgency_types:
            for i in range(count_per_type):
                # Cycle through ambiguity levels
                ambiguity = self.ambiguity_levels[i % len(self.ambiguity_levels)]
                if urgency == "ambiguous":
                    # For ambiguous urgency, use higher ambiguity levels
                    ambiguity = "high" if i < 3 else "medium"
                
                tasks.append(self.generate_referral(specialty, urgency, ambiguity))
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        return [r for r in results if r is not None and not isinstance(r, Exception)]
    
    def save_referrals(self, referrals: List[Dict], specialty: str):
        """Save referrals to JSON file."""
        
        filename = self.config.resolve_path('specialty_referrals', specialty=specialty.lower())
        
        with open(filename, 'w') as f:
            json.dump({
                "specialty": specialty,
                "total_samples": len(referrals),
                "generated_at": datetime.now().isoformat(),
                "referrals": referrals
            }, f, indent=2)
        
        print(f"Saved {len(referrals)} referrals to {filename}")
    
    async def generate_all_referrals(self):
        """Generate all 90 synthetic referrals."""
        
        print("Starting synthetic referral generation...")
        gen_params = self.config.get_generation_params()
        print(f"Target: {gen_params['target_samples_total']} samples across {len(self.specialties)} specialties")
        print(f"Distribution: {gen_params['samples_per_specialty']} samples per specialty ({gen_params['samples_per_urgency_type']} urgent, {gen_params['samples_per_urgency_type']} non-urgent, {gen_params['samples_per_urgency_type']} ambiguous)")
        
        all_referrals = []
        
        for specialty in self.specialties:
            print(f"\nGenerating referrals for {specialty}...")
            
            specialty_referrals = await self.generate_batch(specialty, count_per_type=5)
            
            if specialty_referrals:
                self.save_referrals(specialty_referrals, specialty)
                all_referrals.extend(specialty_referrals)
                print(f"✓ Generated {len(specialty_referrals)} referrals for {specialty}")
            else:
                print(f"✗ Failed to generate referrals for {specialty}")
        
        # Save combined dataset
        combined_filename = self.config.get_data_path('all_referrals')
        with open(combined_filename, 'w') as f:
            json.dump({
                "total_samples": len(all_referrals),
                "specialties": self.specialties,
                "generated_at": datetime.now().isoformat(),
                "distribution": {
                    specialty: len([r for r in all_referrals if r["expected_specialty"] == specialty])
                    for specialty in self.specialties
                },
                "referrals": all_referrals
            }, f, indent=2)
        
        print(f"\n✓ Generation complete!")
        print(f"✓ Total samples generated: {len(all_referrals)}")
        print(f"✓ Combined dataset saved to: {combined_filename}")
        
        return all_referrals

async def main():
    generator = SyntheticReferralGenerator()
    await generator.generate_all_referrals()

if __name__ == "__main__":
    asyncio.run(main())