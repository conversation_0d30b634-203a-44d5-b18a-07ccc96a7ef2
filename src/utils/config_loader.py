"""
Configuration loader utility for the LLM Triage System.
Centralizes loading of all configuration files.
"""

import json
import os
from typing import Dict, Any, List
from pathlib import Path


class ConfigLoader:
    """Loads and provides access to configuration files."""
    
    def __init__(self, config_dir: str = "config"):
        self.config_dir = Path(config_dir)
        self._configs = {}
        self._load_all_configs()
    
    def _load_all_configs(self):
        """Load all configuration files."""
        config_files = {
            'model': 'model_config.json',
            'specialties': 'specialties_config.json', 
            'api': 'api_config.json',
            'paths': 'paths_config.json'
        }
        
        for key, filename in config_files.items():
            config_path = self.config_dir / filename
            if config_path.exists():
                with open(config_path, 'r', encoding='utf-8') as f:
                    self._configs[key] = json.load(f)
            else:
                raise FileNotFoundError(f"Configuration file not found: {config_path}")
    
    def get_model_config(self) -> Dict[str, Any]:
        """Get model configuration."""
        return self._configs['model']
    
    def get_specialties_config(self) -> Dict[str, Any]:
        """Get specialties configuration."""
        return self._configs['specialties']
    
    def get_api_config(self) -> Dict[str, Any]:
        """Get API configuration."""
        return self._configs['api']
    
    def get_paths_config(self) -> Dict[str, Any]:
        """Get paths configuration."""
        return self._configs['paths']
    
    # Convenience methods for commonly used values
    
    def get_specialties_list(self) -> List[str]:
        """Get list of specialty codes."""
        return [spec['code'] for spec in self._configs['specialties']['medical_specialties']]
    
    def get_specialty_contexts(self) -> Dict[str, str]:
        """Get specialty context mapping for data generation."""
        return {
            spec['code']: spec['context'] 
            for spec in self._configs['specialties']['medical_specialties']
        }
    
    def get_llm_config(self, model_type: str = 'data_generation') -> Dict[str, Any]:
        """Get LLM configuration for specific model type."""
        return self._configs['model']['llm_models'].get(model_type, {})
    
    def get_generation_params(self) -> Dict[str, Any]:
        """Get data generation parameters."""
        return self._configs['model']['generation_parameters']
    
    def get_retry_config(self) -> Dict[str, Any]:
        """Get retry configuration."""
        return self._configs['model']['retry_configuration']
    
    def get_api_url(self) -> str:
        """Get triage API URL."""
        return self._configs['api']['api_endpoints']['triage_api']['url']
    
    def get_default_client_id(self) -> str:
        """Get default client ID."""
        return self._configs['api']['api_endpoints']['triage_api']['default_client_id']
    
    def get_file_template(self, template_name: str) -> str:
        """Get file path template."""
        return self._configs['paths']['file_templates'].get(template_name, '')
    
    def get_data_path(self, path_name: str) -> str:
        """Get data path."""
        input_paths = self._configs['paths']['data_paths']['input']
        output_paths = self._configs['paths']['data_paths']['output']
        
        return input_paths.get(path_name) or output_paths.get(path_name, '')
    
    def resolve_path(self, template_name: str, **kwargs) -> str:
        """Resolve a path template with parameters."""
        template = self.get_file_template(template_name)
        if template:
            return template.format(**kwargs)
        return ""


# Global configuration instance
_config_loader = None

def get_config() -> ConfigLoader:
    """Get global configuration loader instance."""
    global _config_loader
    if _config_loader is None:
        _config_loader = ConfigLoader()
    return _config_loader


def reload_config():
    """Reload configuration files."""
    global _config_loader
    _config_loader = None
    return get_config()