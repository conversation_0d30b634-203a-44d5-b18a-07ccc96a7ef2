import json
import time
import asyncio
import aiohttp
import random
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from datetime import datetime
import pandas as pd
from sklearn.metrics import accuracy_score, precision_recall_fscore_support, confusion_matrix, classification_report
import os
from src.utils.config_loader import get_config

@dataclass
class EvaluationResult:
    """Data class to store individual evaluation results."""
    referral_id: int
    referral_text: str
    expected_specialty: str
    expected_urgency: Optional[int]
    predicted_specialty: Optional[str]
    predicted_urgency: Optional[int]
    supporting_evidence:Optional[str]
    confidence_score:Optional[str]
    api_response_time: float
    success: bool
    error: Optional[str]
    retry_count: int = 0
    total_attempts: int = 1


class LLMTriageEvaluator:
    """Evaluates the LLM Triage API performance."""
    
    def __init__(self, api_url: str = None, dataset_path: str = None):
        self.config = get_config()
        
        # Use config defaults if not provided
        self.api_url = api_url or self.config.get_api_url()
        self.dataset_path = dataset_path or self.config.get_data_path('all_referrals')
        
        self.results: List[EvaluationResult] = []
        self.specialties = self.config.get_specialties_list()
        
        # Load retry configuration
        self.retry_config = self.config.get_retry_config()
        
    def load_dataset(self, limit: Optional[int] = None) -> List[Dict]:
        """Load the synthetic referrals dataset.
        
        Args:
            limit: Maximum number of samples to load. None loads all samples.
        
        Returns:
            List of referral dictionaries
        """
        with open(self.dataset_path, 'r') as f:
            data = json.load(f)
        
        referrals = data['referrals']
        if limit is not None:
            return referrals[:limit]
        return referrals
    
    async def call_api_with_retry(self, session: aiohttp.ClientSession, referral_text: str, max_retries: int = None) -> Tuple[Dict, float, int, int]:
        """Make an async API call with exponential backoff retry logic.
        Returns: (response_dict, total_time, retry_count, total_attempts)
        """
        payload = {
            "client_id": self.config.get_default_client_id(),
            "referral_text": referral_text
        }
        
        if max_retries is None:
            max_retries = self.retry_config['max_retries']
        
        retry_count = 0
        total_attempts = 1
        base_delay = self.retry_config.get('retry_delay_multiplier', 1.0)
        max_delay = 30.0  # Maximum delay of 30 seconds
        
        overall_start_time = time.time()
        
        for attempt in range(max_retries + 1):
            try:
                start_time = time.time()
                timeout = self.config.get_api_config()['api_endpoints']['triage_api']['timeout_seconds']
                async with session.post(self.api_url, json=payload, timeout=timeout) as response:
                    response_time = time.time() - start_time
                    
                    if response.status == 200:
                        result = await response.json()
                        total_time = time.time() - overall_start_time
                        return result, total_time, retry_count, total_attempts
                    
                    # Handle different error types
                    error_text = await response.text()
                    error_dict = {"error": f"HTTP {response.status}: {error_text}"}
                    
                    # Parse error details for better handling
                    is_throttling = False
                    try:
                        if error_text:
                            error_json = json.loads(error_text) if error_text.startswith('{') else {"error": error_text}
                            error_msg = error_json.get("error", "").lower()
                            is_throttling = ("throttling" in error_msg or "too many requests" in error_msg or 
                                           "rate limit" in error_msg or "quota exceeded" in error_msg)
                    except:
                        pass
                    
                    # Check if this is an error that should be retried
                    should_retry = (response.status in [429, 500, 502, 503, 504] or is_throttling)
                    
                    if should_retry and attempt < max_retries:
                        # Calculate exponential backoff with jitter
                        delay = min(base_delay * (2 ** attempt), max_delay)
                        jitter = random.uniform(0.1, 0.3) * delay
                        total_delay = delay + jitter
                        
                        error_type = "throttling" if is_throttling else f"status {response.status}"
                        print(f"API call failed ({error_type}), retrying in {total_delay:.2f}s (attempt {attempt + 1}/{max_retries + 1})")
                        await asyncio.sleep(total_delay)
                        retry_count += 1
                        total_attempts += 1
                        continue
                    
                    # Non-retryable error or max retries reached
                    total_time = time.time() - overall_start_time
                    return error_dict, total_time, retry_count, total_attempts
                    
            except asyncio.TimeoutError:
                if attempt < max_retries:
                    delay = min(base_delay * (2 ** attempt), max_delay)
                    jitter = random.uniform(0.1, 0.3) * delay
                    total_delay = delay + jitter
                    
                    print(f"API call timed out, retrying in {total_delay:.2f}s (attempt {attempt + 1}/{max_retries + 1})")
                    await asyncio.sleep(total_delay)
                    retry_count += 1
                    total_attempts += 1
                    continue
                else:
                    total_time = time.time() - overall_start_time
                    return {"error": "Request timeout after retries"}, total_time, retry_count, total_attempts
                    
            except Exception as e:
                # For non-network errors, don't retry
                total_time = time.time() - overall_start_time
                return {"error": str(e)}, total_time, retry_count, total_attempts
        
        # Should not reach here, but just in case
        total_time = time.time() - overall_start_time
        return {"error": "Max retries exceeded"}, total_time, retry_count, total_attempts
        
    def parse_api_response(self, response: Dict) -> Tuple[Optional[str], Optional[int], Optional[float], Optional[float]]:
        """Parse API response to extract predictions and timing info."""
        if "error" in response:
            return None, None, None, None
            
        # Extract predictions directly from response (not from 'body' field)
        predicted_specialty = response.get('specialty')
        predicted_urgency = response.get('urgency_status')
        supporting_evidence = response.get('supporting_evidence')
        confidence_score = response.get('confidence_score')

        
        
            
        return predicted_specialty, predicted_urgency,supporting_evidence,confidence_score
    
    def _is_specialty_match(self, expected: str, predicted: str) -> bool:
        """Check if expected and predicted specialties match, considering equivalencies."""
        if not expected or not predicted:
            return False
            
        # Direct match
        if expected == predicted:
            return True
            
        
            
        return False
    
    def _format_confusion_matrix(self, matrix, labels=None):
        """Format confusion matrix as a readable table."""
        if labels is None:
            labels = self.specialties
        
        # Create header
        header = "     " + " ".join(f"{label[:8]:>8}" for label in labels)
        
        # Create rows
        rows = []
        for i, row in enumerate(matrix):
            label = labels[i][:8] if i < len(labels) else f"Row{i}"
            row_str = f"{label:<8} " + " ".join(f"{val:>8}" for val in row)
            rows.append(row_str)
        
        return "```\n" + header + "\n" + "\n".join(rows) + "\n```"
    
    async def evaluate_dataset(self, max_concurrent: int = 1) -> List[EvaluationResult]:
        """Evaluate the entire dataset with concurrent requests. 
        
        Note: Using sequential processing (1) to minimize Bedrock throttling based on CloudWatch logs.
        """
        referrals = self.load_dataset(limit=90)
        
        print(f"Starting evaluation of {len(referrals)} referrals...")
        print(f"API URL: {self.api_url}")
        print(f"Max concurrent requests: {max_concurrent}")
        perf_settings = self.config.get_api_config()['performance_settings']
        print(perf_settings['throttling_message'])
        print("Retry logic: Up to 3 retries with exponential backoff for failed requests")
        
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def evaluate_single(referral_data: Dict, referral_id: int) -> EvaluationResult:
            async with semaphore:
                # Add delay between requests to reduce throttling
                if referral_id > 0:
                    await asyncio.sleep(1.0)  # 1000ms delay between requests
                
                async with aiohttp.ClientSession() as session:
                    response, api_response_time, retry_count, total_attempts = await self.call_api_with_retry(
                        session, referral_data['referral_text']
                    )
                    
                    predicted_specialty, predicted_urgency,supporting_evidence,confidence_score = self.parse_api_response(response)
                    
                    success = "error" not in response
                    error = response.get("error") if not success else None
                    
                    # Classify error types based on CloudWatch logs analysis
                    if not success and error:
                        if "ThrottlingException" in error or "Too many requests" in error or "429" in error:
                            error = "Bedrock ThrottlingException: Too many requests to underlying model"
                        elif "HTTP 500" in error:
                            # Check if this is a persistent 500 after retries
                            if total_attempts > 1:
                                error = f"Persistent API Error (after {total_attempts} attempts): {error}"
                            else:
                                error = f"API Gateway/Lambda Error: {error}"
                        elif "HTTP 502" in error or "HTTP 503" in error or "HTTP 504" in error:
                            error = f"API Gateway/Lambda Error: {error}"
                        elif "timeout" in error.lower():
                            error = f"Request Timeout: {error}"
                    
                    return EvaluationResult(
                        referral_id=referral_id,
                        referral_text=referral_data['referral_text'],
                        expected_specialty=referral_data['expected_specialty'],
                        expected_urgency=referral_data.get('expected_urgency_status'),
                        predicted_specialty=predicted_specialty,
                        predicted_urgency=predicted_urgency,
                        supporting_evidence=supporting_evidence,
                        confidence_score=confidence_score,
                        api_response_time=api_response_time,
                        success=success,
                        error=error,
                        retry_count=retry_count,
                        total_attempts=total_attempts
                    )
        
        # Execute all evaluations concurrently
        tasks = [evaluate_single(referral, i) for i, referral in enumerate(referrals)]
        self.results = await asyncio.gather(*tasks)
        
        print(f"Evaluation completed. Success rate: {sum(1 for r in self.results if r.success)}/{len(self.results)}")
        
        return self.results
    
    def calculate_specialty_metrics(self) -> Dict:
        """Calculate specialty classification metrics."""
        successful_results = [r for r in self.results if r.success and r.predicted_specialty is not None]
        
        if not successful_results:
            return {"error": "No successful specialty predictions"}
        
        y_true = [r.expected_specialty for r in successful_results]
        y_pred = [r.predicted_specialty for r in successful_results]
        
        # Create custom accuracy calculation 
        correct_predictions = sum(1 for r in successful_results if self._is_specialty_match(r.expected_specialty, r.predicted_specialty))
        accuracy = correct_predictions / len(successful_results)
        
        # For precision/recall/f1, we'll use the standard metrics but note the HEMATOLOGY equivalence in reporting
        precision, recall, f1, support = precision_recall_fscore_support(y_true, y_pred, average='weighted')
        
        # Per-class metrics
        precision_per_class, recall_per_class, f1_per_class, support_per_class = precision_recall_fscore_support(
            y_true, y_pred, average=None, labels=self.specialties
        )
        
        # Confusion matrix
        cm = confusion_matrix(y_true, y_pred, labels=self.specialties)
        
        return {
            "overall": {
                "accuracy": accuracy,
                "precision": precision,
                "recall": recall,
                "f1_score": f1,
                "total_samples": len(successful_results)
            },
            "per_class": {
                specialty: {
                    "precision": precision_per_class[i],
                    "recall": recall_per_class[i],
                    "f1_score": f1_per_class[i],
                    "support": support_per_class[i]
                }
                for i, specialty in enumerate(self.specialties)
            },
            "confusion_matrix": cm.tolist(),
            "classification_report": classification_report(y_true, y_pred, labels=self.specialties)
        }
    
    def calculate_urgency_metrics(self) -> Dict:
        """Calculate urgency detection metrics."""
        successful_results = [
            r for r in self.results 
            if r.success and r.predicted_urgency is not None and r.expected_urgency is not None
        ]
        
        if not successful_results:
            return {"error": "No successful urgency predictions"}
        
        y_true = [r.expected_urgency for r in successful_results]
        y_pred = [r.predicted_urgency for r in successful_results]
        
        # Calculate metrics
        accuracy = accuracy_score(y_true, y_pred)
        precision, recall, f1, support = precision_recall_fscore_support(y_true, y_pred, average='binary')
        
        # Confusion matrix
        cm = confusion_matrix(y_true, y_pred, labels=[0, 1])
        
        return {
            "accuracy": accuracy,
            "precision": precision,
            "recall": recall,
            "f1_score": f1,
            "total_samples": len(successful_results),
            "confusion_matrix": cm.tolist(),
            "classification_report": classification_report(y_true, y_pred, labels=[0, 1], target_names=['Non-Urgent', 'Urgent'])
        }
    
    def calculate_performance_metrics(self) -> Dict:
        """Calculate API and Lambda performance metrics."""
        successful_results = [r for r in self.results if r.success]
        
        if not successful_results:
            return {"error": "No successful API calls"}
        
        api_response_times = [r.api_response_time for r in successful_results]
        
        
        def calc_stats(values: List[float]) -> Dict:
            if not values:
                return {"error": "No data available"}
            return {
                "mean": sum(values) / len(values),
                "min": min(values),
                "max": max(values),
                "p50": sorted(values)[len(values) // 2],
                "p95": sorted(values)[int(len(values) * 0.95)],
                "p99": sorted(values)[int(len(values) * 0.99)],
                "count": len(values)
            }
        
        # Calculate retry statistics
        total_retries = sum(r.retry_count for r in self.results)
        total_attempts = sum(r.total_attempts for r in self.results)
        avg_retries_per_request = total_retries / len(self.results) if self.results else 0
        requests_with_retries = sum(1 for r in self.results if r.retry_count > 0)
        
        return {
            "api_response_time": calc_stats(api_response_times),
            "success_rate": len(successful_results) / len(self.results),
            "total_requests": len(self.results),
            "successful_requests": len(successful_results),
            "failed_requests": len(self.results) - len(successful_results),
            "retry_stats": {
                "total_retries": total_retries,
                "total_attempts": total_attempts,
                "avg_retries_per_request": avg_retries_per_request,
                "requests_with_retries": requests_with_retries,
                "retry_rate": requests_with_retries / len(self.results) if self.results else 0
            }
        }
    
    def generate_report(self) -> str:
        """Generate a comprehensive evaluation report."""
        specialty_metrics = self.calculate_specialty_metrics()
        urgency_metrics = self.calculate_urgency_metrics()
        performance_metrics = self.calculate_performance_metrics()
        
        report = f"""# LLM Triage API Evaluation Report

## Evaluation Overview

- **API Endpoint**: {self.api_url}
- **Dataset**: {self.dataset_path}
- **Total Samples**: {len(self.results)}
- **Evaluation Date**: {datetime.now().isoformat()}

## Performance Metrics

### API Performance
- **Success Rate**: {performance_metrics.get('success_rate', 0):.2%}
- **Total Requests**: {performance_metrics.get('total_requests', 0)}
- **Failed Requests**: {performance_metrics.get('failed_requests', 0)}

### Response Time Analysis
"""
        
        if 'api_response_time' in performance_metrics and 'error' not in performance_metrics['api_response_time']:
            api_stats = performance_metrics['api_response_time']
            report += f"""
#### API Gateway + Lambda Total Response Time
- **Mean**: {api_stats['mean']:.3f}s
- **Median (P50)**: {api_stats['p50']:.3f}s
- **95th Percentile**: {api_stats['p95']:.3f}s
- **99th Percentile**: {api_stats['p99']:.3f}s
- **Min**: {api_stats['min']:.3f}s
- **Max**: {api_stats['max']:.3f}s
"""
        
        
        # Specialty Classification Results
        if 'error' not in specialty_metrics:
            report += f"""
## Specialty Classification Results


### Overall Metrics
- **Accuracy**: {specialty_metrics['overall']['accuracy']:.3f}
- **Precision**: {specialty_metrics['overall']['precision']:.3f}
- **Recall**: {specialty_metrics['overall']['recall']:.3f}
- **F1-Score**: {specialty_metrics['overall']['f1_score']:.3f}
- **Total Samples**: {specialty_metrics['overall']['total_samples']}
### Confusion Matrix
{self._format_confusion_matrix(specialty_metrics["confusion_matrix"])}

### Per-Specialty Performance
"""
            for specialty in self.specialties:
                if specialty in specialty_metrics['per_class']:
                    metrics = specialty_metrics['per_class'][specialty]
                    report += f"""
#### {specialty}
- **Precision**: {metrics['precision']:.3f}
- **Recall**: {metrics['recall']:.3f}
- **F1-Score**: {metrics['f1_score']:.3f}
- **Support**: {int(metrics['support'])}
"""
        
        # Urgency Detection Results
        if 'error' not in urgency_metrics:
            report += f"""
## Urgency Detection Results

- **Accuracy**: {urgency_metrics['accuracy']:.3f}
- **Precision**: {urgency_metrics['precision']:.3f}
- **Recall**: {urgency_metrics['recall']:.3f}
- **F1-Score**: {urgency_metrics['f1_score']:.3f}
- **Total Samples**: {urgency_metrics['total_samples']}

### Classification report (Non-Urgent vs Urgent)
```
{urgency_metrics['classification_report']}
```

### Confusion Matrix (Non-Urgent vs Urgent)
{self._format_confusion_matrix(urgency_metrics['confusion_matrix'], ['Non-Urgent', 'Urgent'])}
"""
        
        # Error Analysis
        failed_results = [r for r in self.results if not r.success]
        if failed_results:
            report += f"""
## Error Analysis

### Failed Requests: {len(failed_results)}

"""
            error_counts = {}
            for result in failed_results:
                error = result.error or "Unknown error"
                error_counts[error] = error_counts.get(error, 0) + 1
            
            for error, count in sorted(error_counts.items(), key=lambda x: x[1], reverse=True):
                report += f"- **{error}**: {count} occurrences\n"
        
        report += f"""
## Summary and Recommendations

### Key Findings
1. **API Reliability**: {performance_metrics.get('success_rate', 0):.1%} success rate across {len(self.results)} requests
2. **Response Time**: Average end-to-end response time of {performance_metrics.get('api_response_time', {}).get('mean', 0):.3f}s
"""
        
        if 'error' not in specialty_metrics:
            report += f"3. **Specialty Classification**: {specialty_metrics['overall']['accuracy']:.1%} accuracy with F1-score of {specialty_metrics['overall']['f1_score']:.3f}\n"
        
        if 'error' not in urgency_metrics:
            report += f"4. **Urgency Detection**: {urgency_metrics['accuracy']:.1%} accuracy with F1-score of {urgency_metrics['f1_score']:.3f}\n"
        
        report += """
### Recommendations
1. Monitor API Gateway and Lambda metrics in CloudWatch for performance optimization
2. Consider implementing retry logic for failed requests
3. Analyze per-specialty performance to identify areas for model improvement
4. Set up alerts for response time degradation and error rate increases

---
*Report generated by LLM Triage Evaluator*
"""
        
        return report
    
    def save_detailed_results(self, output_dir: str):
        """Save detailed results to CSV and JSON files."""
        os.makedirs(output_dir, exist_ok=True)
        
        # Convert results to DataFrame for easy analysis
        results_data = []
        for result in self.results:
            results_data.append({
                'referral_id': result.referral_id,
                'expected_specialty': result.expected_specialty,
                'predicted_specialty': result.predicted_specialty,
                'specialty_correct': self._is_specialty_match(result.expected_specialty, result.predicted_specialty) if result.predicted_specialty else False,
                'expected_urgency': result.expected_urgency,
                'predicted_urgency': result.predicted_urgency,
                'urgency_correct': result.expected_urgency == result.predicted_urgency if result.predicted_urgency is not None and result.expected_urgency is not None else None,
                'supporting_evidence':result.supporting_evidence,
                'confidence_score':result.confidence_score,
                'api_response_time': result.api_response_time,
                'success': result.success,
                'error': result.error,
                'retry_count': result.retry_count,
                'total_attempts': result.total_attempts
            })
        
        df = pd.DataFrame(results_data)
        
        # Save to CSV
        time_stamp = datetime.now().strftime(self.config.get_paths_config()['file_formats']['timestamp_format'])
        csv_path = self.config.resolve_path('evaluation_csv', timestamp=time_stamp)
        df.to_csv(csv_path, index=False)
        
        # Save to JSON with full details
        json_data = {
            'evaluation_metadata': {
                'api_url': self.api_url,
                'dataset_path': self.dataset_path,
                'total_samples': len(self.results),
                'evaluation_timestamp': datetime.now().isoformat()
            },
            'results': [
                {
                    'referral_id': r.referral_id,
                    'referral_text': r.referral_text,
                    'expected_specialty': r.expected_specialty,
                    'expected_urgency': r.expected_urgency,
                    'predicted_specialty': r.predicted_specialty,
                    'predicted_urgency': r.predicted_urgency,
                    'supporting_evidence':result.supporting_evidence,
                    'confidence_score':result.confidence_score,
                    'api_response_time': r.api_response_time,
                    'success': r.success,
                    'error': r.error,
                    'retry_count': r.retry_count,
                    'total_attempts': r.total_attempts
                }
                for r in self.results
            ]
        }
        
        json_path = self.config.resolve_path('evaluation_json', timestamp=time_stamp)
        with open(json_path, 'w') as f:
            json.dump(json_data, f, indent=2)
        
        print(f"Detailed results saved to:")
        print(f"  - CSV: {csv_path}")
        print(f"  - JSON: {json_path}")

async def main():
    """Main evaluation function."""
    # Initialize evaluator with config defaults
    evaluator = LLMTriageEvaluator()
    output_dir = evaluator.config.get_data_path('evaluation_results_dir')
    
    # Run evaluation
    print("Starting LLM Triage API evaluation...")
    await evaluator.evaluate_dataset(max_concurrent=1)  # Sequential processing to minimize throttling
    
    # Generate and save report
    report = evaluator.generate_report()
    
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # Save markdown report
    time_stamp = datetime.now().strftime(evaluator.config.get_paths_config()['file_formats']['timestamp_format'])
    report_path = evaluator.config.resolve_path('evaluation_report', timestamp=time_stamp)
    with open(report_path, 'w') as f:
        f.write(report)
    
    print(f"Evaluation report saved to: {report_path}")
    
    # Save detailed results
    evaluator.save_detailed_results(output_dir)
    
    print("\nEvaluation completed successfully!")

if __name__ == "__main__":
    asyncio.run(main())