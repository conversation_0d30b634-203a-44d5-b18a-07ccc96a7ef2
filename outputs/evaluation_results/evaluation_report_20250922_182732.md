# LLM Triage API Evaluation Report

## Evaluation Overview

- **API Endpoint**: https://l4sstl0qe6.execute-api.us-west-2.amazonaws.com/default/LlmTriageFunction
- **Dataset**: data/processed/referral_samples/all_referrals.json
- **Total Samples**: 90
- **Evaluation Date**: 2025-09-22T18:27:32.306858

## Performance Metrics

### API Performance
- **Success Rate**: 100.00%
- **Total Requests**: 90
- **Failed Requests**: 0

### Response Time Analysis

#### API Gateway + Lambda Total Response Time
- **Mean**: 10.398s
- **Median (P50)**: 10.979s
- **95th Percentile**: 14.059s
- **99th Percentile**: 16.412s
- **Min**: 2.138s
- **Max**: 16.412s

## Specialty Classification Results


### Overall Metrics
- **Accuracy**: 0.989
- **Precision**: 0.990
- **Recall**: 0.989
- **F1-Score**: 0.989
- **Total Samples**: 90
### Confusion Matrix
```
     CARDIOLO NEUROLOG GYNECOLO HEMATOLO ORTHOPED NUTRITIO
CARDIOLO       15        0        0        0        0        0
NEUROLOG        0       15        0        0        0        0
GYNECOLO        0        0       15        0        0        0
HEMATOLO        0        0        0       15        0        0
ORTHOPED        1        0        0        0       14        0
NUTRITIO        0        0        0        0        0       15
```

### Per-Specialty Performance

#### CARDIOLOGY
- **Precision**: 0.938
- **Recall**: 1.000
- **F1-Score**: 0.968
- **Support**: 15

#### NEUROLOGY
- **Precision**: 1.000
- **Recall**: 1.000
- **F1-Score**: 1.000
- **Support**: 15

#### GYNECOLOGY
- **Precision**: 1.000
- **Recall**: 1.000
- **F1-Score**: 1.000
- **Support**: 15

#### HEMATOLOGY_AND_ONCOLOGY
- **Precision**: 1.000
- **Recall**: 1.000
- **F1-Score**: 1.000
- **Support**: 15

#### ORTHOPEDICS
- **Precision**: 1.000
- **Recall**: 0.933
- **F1-Score**: 0.966
- **Support**: 15

#### NUTRITION
- **Precision**: 1.000
- **Recall**: 1.000
- **F1-Score**: 1.000
- **Support**: 15

## Urgency Detection Results

- **Accuracy**: 0.650
- **Precision**: 0.588
- **Recall**: 1.000
- **F1-Score**: 0.741
- **Total Samples**: 60

### Classification report (Non-Urgent vs Urgent)
```
              precision    recall  f1-score   support

  Non-Urgent       1.00      0.30      0.46        30
      Urgent       0.59      1.00      0.74        30

    accuracy                           0.65        60
   macro avg       0.79      0.65      0.60        60
weighted avg       0.79      0.65      0.60        60

```

### Confusion Matrix (Non-Urgent vs Urgent)
```
     Non-Urge   Urgent
Non-Urge        9       21
Urgent          0       30
```

## Conclusions and Analysis
- Urgency Classification: The model is strongly biased towards flagging cases as urgent. While it achieves perfect recall (finding all true urgent cases), its precision is very low (59%), leading to an excessive number of false positives (21 out of 30 non-urgent cases were misclassified). This suggests the current prompt and rules make the model overly cautious.

- Specialty Classification: The system is highly accurate (98.9%) at specialty classification. The single error, misclassifying an Orthopedics case as Cardiology (referral_id: 70), was due to the presence of significant cardiac-related keywords ("irregular heart rhythm," "syncopal episodes") that the model prioritized over the primary complaint of knee pain.

- Confidence Score: The confidence_score remains static (0.7 for all 90 samples) despite multiple prompt revisions aimed at making it dynamic. This indicates a persistent failure of the LLM to adhere to this specific instruction, likely treating it as a low-priority task compared to the primary classifications.

- API Response Time: The API's average response time is very high at 10.4 seconds. This latency is almost entirely driven by the "cold start" and inference time of the Bedrock LLM call, making it too slow for an efficient, real-time clinical workflow.

## Suggestions for Improvement
- Prompt Engineering: To fix the urgency bias, the prompt should be re-engineered with a "chain-of-thought" approach that explicitly requires the model to first state why a case is urgent by quoting a specific rule, and only then assign the urgency status. This forces stricter adherence to the provided rules.

- Rule Enhancement: The rules in DynamoDB should be enhanced with more context and negative constraints. For example, the Orthopedics rule should specify that systemic symptoms like "irregular heart rhythm" do not automatically warrant an urgent orthopedic referral, guiding the model to weigh the primary complaint more heavily.

- Confidence Score: The confidence score task should be simplified. Instead of a float, ask for a simple string classification ("High", "Medium", "Low") based on the rubric. This is often an easier task for an LLM to follow consistently than generating a nuanced floating-point number.

- Latency Reduction: To significantly reduce the 10.4-second average latency, the best approach is to switch from a large, general-purpose model (like Claude 3 Sonnet) to a smaller, fine-tuned, and self-hosted model (e.g., a member of the model families finetuned in healthcare data such as Meditron by EPFL or Med-PaLM M by Google) on a dedicated Amazon SageMaker endpoint. This eliminates cold starts and provides consistent, sub-2-second inference times.
