[build-system]
requires = ["setuptools>=45", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "titan-llm-triage"
version = "1.0.0"
description = "LLM Medical Triage System with MLOps Integration"
authors = [{name = "Titan Team"}]
dependencies = [
    "asyncio",
    "aiohttp", 
    "pandas",
    "scikit-learn",
    "anthropic",
    "python-dotenv",
    "boto3"
]

[project.optional-dependencies]
dev = [
    "pytest",
    "pytest-cov",
    "black",
    "flake8",
    "mypy"
]

[tool.black]
line-length = 100
target-version = ['py39']

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = "test_*.py"
python_classes = "Test*"
python_functions = "test_*"
