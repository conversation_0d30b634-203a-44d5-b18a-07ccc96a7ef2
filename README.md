# LLM Medical Triage System

A comprehensive system for medical referral triage using Large Language Models (LLMs) with MLOps integration.

## Project Structure

```
├── src/                    # Source code
├── infrastructure/         # Infrastructure as Code  
├── scripts/               # Deployment & utility scripts
├── data/                  # Data management
├── outputs/               # Generated outputs
├── docs/                  # Documentation
└── config/                # Application configuration
```

## Quick Start

1. **Environment Setup**:
   ```bash
   cp .env.example .env  # Configure your environment variables
   pip install -r requirements.txt
   ```

2. **Data Generation**:
   ```bash
   python -m src.data_generation.synthetic_referrals
   ```

3. **API Evaluation**:
   ```bash
   python -m src.evaluation.llm_triage_evaluator
   ```

4. **Infrastructure Deployment**:
   ```bash
   cd scripts/deployment
   ./deploy.sh
   ```
5. **LLM triage model string**:
```anthropic.claude-3-sonnet-20240229-v1:0```


## Documentation


- [Report of Evaluation Results](outputs/evaluation_results/evaluation_report_20250922_182732.md)
- [CSV of Evaluation Results](outputs/evaluation_results/detailed_results_20250922_182732.csv)
- [JSON of Evaluation Results](outputs/evaluation_results/detailed_results20250922_182732.json)
- [API Documentation](docs/api.md)
- [Deployment Guide](docs/deployment.md) 
- [Evaluation Methodology](docs/evaluation.md)
- [Architecture Overview](docs/architecture.md)




