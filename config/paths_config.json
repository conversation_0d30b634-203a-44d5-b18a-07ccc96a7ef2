{"data_paths": {"input": {"referral_samples_dir": "data/processed/referral_samples", "all_referrals": "data/processed/referral_samples/all_referrals.json", "clinic_rules": "data/config/clinic_rules"}, "output": {"evaluation_results_dir": "outputs/evaluation_results", "generated_data_dir": "data/processed/referral_samples"}}, "file_templates": {"specialty_referrals": "data/processed/referral_samples/{specialty}_referrals.json", "evaluation_csv": "outputs/evaluation_results/detailed_results_{timestamp}.csv", "evaluation_json": "outputs/evaluation_results/detailed_results{timestamp}.json", "evaluation_report": "outputs/evaluation_results/evaluation_report_{timestamp}.md"}, "file_formats": {"timestamp_format": "%Y%m%d_%H%M%S", "encoding": "utf-8", "json_indent": 2}}