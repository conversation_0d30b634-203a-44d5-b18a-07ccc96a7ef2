# Configuration Files

This directory contains centralized configuration for the LLM Triage System.

## Configuration Files

### `model_config.json`
- LLM model settings for data generation and evaluation
- Generation parameters (samples, urgency types, etc.)
- Retry configuration for API calls

### `specialties_config.json` 
- Medical specialties definitions and contexts
- Keywords for each specialty

### `api_config.json`
- API endpoint configurations
- Performance settings and rate limits
- Error handling configuration

### `paths_config.json`
- File and directory path templates
- Input/output directory mappings
- File naming and format conventions

## Usage

Load configuration in Python code:

```python
from src.utils.config_loader import get_config

config = get_config()

# Get specialties list
specialties = config.get_specialties_list()

# Get LLM configuration
llm_config = config.get_llm_config('data_generation')

# Get API URL
api_url = config.get_api_url()

# Resolve file path template
csv_path = config.resolve_path('evaluation_csv', timestamp='20250920_210318')
```

## Configuration Loader

The `src/utils/config_loader.py` module provides a centralized interface to load and access all configuration files. It includes convenience methods for commonly used values and path resolution.

## Benefits

- **Centralized**: All settings in one place
- **Environment-agnostic**: Easy to customize for different deployments
- **Type-safe**: JSON schema validation
- **Maintainable**: Clear separation of configuration from code