{"api_endpoints": {"triage_api": {"url": "https://l4sstl0qe6.execute-api.us-west-2.amazonaws.com/default/LlmTriageFunction", "method": "POST", "timeout_seconds": 30, "default_client_id": "clinic-001"}}, "performance_settings": {"concurrency_limit": 1, "rate_limit_per_minute": 100, "throttling_message": "Note: Reduced concurrency to minimize Bedrock ThrottlingException errors"}, "error_handling": {"retryable_status_codes": [429, 500, 502, 503, 504], "retryable_errors": ["timeout", "throttling", "service_unavailable"], "non_retryable_errors": ["authentication", "authorization", "bad_request"]}}